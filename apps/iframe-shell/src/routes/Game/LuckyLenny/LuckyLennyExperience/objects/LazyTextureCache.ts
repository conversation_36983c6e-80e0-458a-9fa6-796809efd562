import { Assets, Sprite, type Texture } from 'pixi.js';

interface ITextureCache {
    [key: string]: string;
}

export class LazyTextureCache<TCache extends ITextureCache> {
    private cache: Partial<Record<keyof TCache, Texture>> = {};

    private readonly donePromise: Promise<void>;

    constructor(props: TCache) {
        this.donePromise = Object.entries(props).reduce((acc, [key, path]) => {
            acc.then(() => {
                return Assets.load(path).then((texture: Texture) => {
                    this.cache[key as keyof TCache] = texture;
                });
            });

            return acc;
        }, Promise.resolve());
    }

    public get done() {
        return this.donePromise;
    }

    public getSprite(key: keyof TCache) {
        const sprite = new Sprite(this.get(key));

        sprite.label = String(key);
        sprite.anchor.set(0.5);

        return sprite;
    }

    public dispose(): void {
        // Clear texture cache - textures are managed by PIXI Assets system
        // so we don't need to destroy them manually, just clear our references
        this.cache = {};
    }

    private get(key: keyof TCache) {
        return this.cache[key];
    }
}
