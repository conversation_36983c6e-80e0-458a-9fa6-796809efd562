import { Container, Sprite, Texture, Assets } from 'pixi.js';
import { LuckyLennyGameId } from '../../enums';
import { EXTENSIONS } from '../../constants';

const MAIN_PATH = '/static/shared/assets/pixi/lenny/transition/';

const GAME_IMAGE_PATHS: Record<LuckyLennyGameId, string> = {
    [LuckyLennyGameId.LennyWheelGame]: `${MAIN_PATH}wheel_game.${EXTENSIONS.PNG}`,
    [LuckyLennyGameId.LennyAztecGame]: `${MAIN_PATH}board_game.${EXTENSIONS.PNG}`,
    [LuckyLennyGameId.LennySlotGame]: `${MAIN_PATH}jetpack.${EXTENSIONS.PNG}`,
};

export class GameTransitionManager {
    private sprite: Sprite | null = null;

    private textureCache: Record<string, Texture> = {};

    constructor(
        private readonly stage: Container,
        private readonly width: number,
        private readonly height: number,
    ) {}

    /**
     * Shows the transition logo for the given game and resolves once the
     * simple zoom animation has finished.
     */
    public async play(gameId: LuckyLennyGameId): Promise<void> {
        const path = this.getImagePath(gameId);

        let texture = this.textureCache[path];
        if (!texture) {
            texture = await Assets.load(path);
            this.textureCache[path] = texture;
        }

        this.sprite = new Sprite(texture);
        this.sprite.anchor.set(0.5);
        this.sprite.position.set(this.width / 2, this.height / 2);
        // start a little bit smaller
        this.sprite.scale.set(0.8);

        this.stage.addChild(this.sprite);

        await this.zoomAnimation();

        this.stage.removeChild(this.sprite);
        this.sprite.destroy();
        this.sprite = null;
    }

    private zoomAnimation(): Promise<void> {
        return new Promise((resolve) => {
            const initialScale = 0.8;
            const peakScale = 1.2;
            const totalDuration = 1000; // milliseconds
            const startTime = Date.now();

            const animate = (): void => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / totalDuration, 1);

                // ease in/out – first half scale up, second half scale down
                if (progress < 0.5) {
                    const t = progress * 2; // 0 -> 1
                    const scale = initialScale + (peakScale - initialScale) * t;
                    this.sprite.scale.set(scale);
                } else {
                    const t = (progress - 0.5) * 2; // 0 -> 1
                    const scale = peakScale - (peakScale - 1) * t;
                    this.sprite.scale.set(scale);
                }

                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    resolve();
                }
            };

            requestAnimationFrame(animate);
        });
    }

    public dispose(): void {
        // Clean up any active sprite
        if (this.sprite) {
            if (this.sprite.parent) {
                this.sprite.parent.removeChild(this.sprite);
            }
            this.sprite.destroy();
            this.sprite = null;
        }

        // Clear texture cache - textures are managed by PIXI Assets system
        // so we don't need to destroy them manually, just clear our references
        this.textureCache = {};
    }

    private getImagePath(gameId: LuckyLennyGameId): string {
        return (
            GAME_IMAGE_PATHS[gameId] ??
            `/static/shared/assets/pixi/lenny/transition/jetpack.${EXTENSIONS.PNG}`
        );
    }
}
