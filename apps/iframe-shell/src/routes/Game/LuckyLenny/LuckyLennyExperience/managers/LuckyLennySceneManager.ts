import { Container, Graphics } from 'pixi.js';
import { Orientation, Logger, envConfig } from '@bg-shared';
import PixiViewport from '@bg-services/Pixi/pixiViewport';
import { EventManager } from '@bg-services';

import { LazyTextureCache } from '../objects/LazyTextureCache';
import { LAYER_ORDER } from '../config';
import type { IGameManager } from '../interfaces/gameManager';
import { BonusBoardGameController } from './games/lennyBoardGame/BonusBoardGameController';
import { SlotGameManager } from './games/lennySlotGame/SlotGameManager';
import { debugShortKeyMap } from '../config/debugShortKeyMap';
import { GameManagerBase } from './games/GameManagerBase';
import { LuckyLennyEvents, LuckyLennyGameId, PixiEventMode } from '../../enums';
import { ArrangementVariant } from './symbols/SymbolManager';
import { BonusWheelController } from './games/lennyWheelGame/BonusWheelController';
import { SymbolSetupPopup } from '../debug/SymbolSetupPopup';
import { LennyController } from './LennyController';
import {
    ILuckyLennySwitchGameEvent,
    ILuckyLennyTotalWinEvent,
    ILuckyLennyTurboEvent,
    IPlayResponse,
} from '../../interfaces';
import { OverlayManager } from './OverlayManager';

export class LuckyLennySceneManager {
    private fadeOverlay: Graphics;

    private fadeContainer: Container;

    private activeGame: GameManagerBase & IGameManager;

    private overlay: OverlayManager;

    private topLevelContainer = new Container();

    private lennyController?: LennyController;

    private symbolSetupPopup: SymbolSetupPopup;

    lastSlotGameResponse: number[];

    // Add orientation and isMobileView properties
    private orientation: Orientation = Orientation.LANDSCAPE;

    private isMobileView = false;

    private isTurboModeEnabled = false;

    private lazyTextureCache: LazyTextureCache<Record<LuckyLennyGameId, string>>;

    private eventUnsubscribers: (() => void)[] = [];

    private resetTimeoutId: number;

    private totalWinTimeoutId: number;

    constructor(
        private readonly stage: Container,
        private readonly viewport: PixiViewport,
        private readonly eventManager: EventManager,
    ) {
        Logger.log(`\n${debugShortKeyMap.map((s) => `  ${s.padEnd(50, ' ')}`).join('\n')}\n`);

        this.overlay = new OverlayManager(this.stage, this.viewport, this.eventManager);

        // Transition helpers are part of OverlayManager now

        this.resetTimeoutId = setTimeout(() => {
            this.reset();
        }, 200);

        this.eventUnsubscribers.push(
            this.eventManager.on<ILuckyLennySwitchGameEvent>(
                LuckyLennyEvents.switchGame,
                ({ gameId, totalWinAmount = 0, symbols }) => {
                    this.switchGame(gameId, symbols);

                    if (totalWinAmount > 0) {
                        this.totalWinTimeoutId = setTimeout(() => {
                            this.eventManager.trigger<ILuckyLennyTotalWinEvent>(
                                LuckyLennyEvents.totalWin,
                                {
                                    amount: totalWinAmount,
                                },
                            );
                        }, 2000);
                    }
                },
            ),
            this.eventManager.on(LuckyLennyEvents.newGame, () => {
                this.switchGame(LuckyLennyGameId.LennySlotGame);
            }),
            this.eventManager.on<ILuckyLennyTurboEvent>(LuckyLennyEvents.turbo, (isTurbo) => {
                this.turboModeEnable(isTurbo);
            }),
            this.eventManager.on([LuckyLennyEvents.winAmount, LuckyLennyEvents.totalWin], () =>
                this.celebrationStart(),
            ),
            this.eventManager.on(
                [LuckyLennyEvents.winAnimationEnd, LuckyLennyEvents.totalWinAnimationEnd],
                () => this.celebrationEnd(),
            ),
        );

        if (envConfig.isDevEnv || envConfig.isStagingEnv) {
            this.symbolSetupPopup = new SymbolSetupPopup(this.stage, this.viewport);
        }

        this.stage.eventMode = PixiEventMode.static;
        this.topLevelContainer.zIndex = LAYER_ORDER.TOP_LEVEL;

        this.stage.addChild(this.topLevelContainer);
    }

    public initialize(): void {
        this.switchGame(LuckyLennyGameId.LennySlotGame);

        this.lazyTextureCache = new LazyTextureCache({
            [LuckyLennyGameId.LennyWheelGame]:
                '/static/shared/assets/pixi/lenny/transition/wheel_game.png',
            [LuckyLennyGameId.LennyAztecGame]:
                '/static/shared/assets/pixi/lenny/transition/board_game.png',
            [LuckyLennyGameId.LennySlotGame]:
                '/static/shared/assets/pixi/lenny/transition/jetpack.png',
        });
    }

    public isTransitioning(): boolean {
        return this.overlay.isTransitioning() || false;
    }

    public setArrangementVariant(variant: ArrangementVariant): void {
        if (LuckyLennyGameId.LennySlotGame === this.activeGame.id) {
            this.activeGame.setArrangementVariant(variant);
        }
    }

    public switchGame(game: LuckyLennyGameId, symbols?: number[]): void {
        this.overlay.setGameId(game);

        // If we are already in the slot game and asked to switch to it again – just reset.
        if (
            game === LuckyLennyGameId.LennySlotGame &&
            this.activeGame?.id === LuckyLennyGameId.LennySlotGame
        ) {
            this.activeGame.reset();
            return;
        }

        const executeSwitch = () => {
            this.activeGame?.dispose();
            this.lennyController?.dispose();

            // Create new game based on game type
            switch (game) {
                case LuckyLennyGameId.LennySlotGame:
                    this.activeGame = new SlotGameManager(
                        this.stage,
                        this.viewport,
                        this.eventManager,
                    );
                    // Setup the slot game manager to ensure proper initialization
                    this.activeGame.setup(this.lastSlotGameResponse);

                    break;
                case LuckyLennyGameId.LennyAztecGame:
                    this.activeGame = new BonusBoardGameController(
                        this.stage,
                        this.viewport,
                        this.eventManager,
                        symbols,
                    );

                    break;
                case LuckyLennyGameId.LennyWheelGame:
                    Logger.log('LuckyLennySceneManager: Creating BonusWheelController');
                    this.activeGame = new BonusWheelController(
                        this.stage,
                        this.viewport,
                        this.eventManager,
                        undefined,
                        undefined,
                    );

                    break;
                default:
                    throw new Error(`Unknown game: ${game}`);
            }

            this.lennyController = new LennyController(this.eventManager);

            this.handleResize(this.orientation, this.isMobileView);
            this.turboModeEnable(this.isTurboModeEnabled);
            this.activeGame.activeLenny(this.lennyController);

            this.viewport.layoutManager.unregister(this.topLevelContainer);
            this.activeGame.registerTopLevelContainer(this.topLevelContainer);
            this.viewport.layoutManager.update();

            // Don't call setup() for BonusBoardGameController as it's already called in constructor

            Logger.log('LuckyLennySceneManager: executeSwitch completed');
        };

        if (!this.activeGame) {
            executeSwitch();
        } else {
            this.lazyTextureCache.done.then(() => {
                this.overlay.performTransition(
                    this.lazyTextureCache.getSprite(game),
                    executeSwitch,
                );

                this.blur(true);
            });
        }
    }

    private blur(enable: boolean) {
        this.activeGame.blur(enable);
    }

    public turboModeEnable(enable: boolean) {
        this.isTurboModeEnabled = enable;
        this.activeGame?.turboModeEnable(enable);
        this.overlay.turboModeEnable(enable);
        this.lennyController?.turboModeEnable(enable);
    }

    public handleResize = (orientation: Orientation, isMobileView: boolean): void => {
        this.orientation = orientation;
        this.isMobileView = isMobileView;
        this.activeGame.handleResize(orientation, isMobileView);
        this.lennyController.handleResize(orientation, isMobileView);
        this.overlay.handleResize();
    };

    public onSpin = (response: IPlayResponse): void => {
        this.activeGame.onSpin(response);
        if (this.activeGame.id === LuckyLennyGameId.LennySlotGame) {
            this.lastSlotGameResponse = response.lastRoundResult?.symbols;
        }
    };

    // Filling the lastSlotGameResponse for the slot game from BE to fill the symbols for the slot game
    // Used for the slot game to have the symbols for the slot game from previous game from BE
    public onStartFromScratch = (symbols: number[]): void => {
        if (this.activeGame.id === LuckyLennyGameId.LennySlotGame) {
            this.lastSlotGameResponse = symbols;
        }
    };

    public onBonusGameboardClickResponse = (response: IPlayResponse, symbolIndex: number): void => {
        // only for board game
        this.activeGame.onBonusGameboardClickResponse(response, symbolIndex);
    };

    public update(): void {
        this.activeGame?.update();
    }

    public reset(): void {
        this.activeGame?.reset();
        this.viewport.resize();
    }

    public dispose(): void {
        clearTimeout(this.resetTimeoutId);
        clearTimeout(this.totalWinTimeoutId);

        if (this.fadeContainer && this.stage) {
            this.stage.removeChild(this.fadeContainer);
        }
        this.viewport.layoutManager.unregister(this.topLevelContainer);
        this.fadeOverlay?.destroy();
        this.fadeContainer?.destroy();
        this.lennyController?.dispose();
        this.activeGame?.dispose();
        this.overlay?.destroy();
        this.lazyTextureCache?.dispose();
        this.eventUnsubscribers.forEach((unsubscribe) => unsubscribe());
        this.eventUnsubscribers = [];

        if (envConfig.isDevEnv || envConfig.isStagingEnv) {
            this.symbolSetupPopup.destroy();
        }
    }

    private celebrationStart() {
        this.lennyController?.bringLennyToContainer(this.topLevelContainer);
        this.lennyController?.celebration();
    }

    private celebrationEnd() {
        this.lennyController?.bringLennyBack();
    }
}
