import { Logger } from '@bg-shared';
import PixiViewport from '@bg-services/Pixi/pixiViewport';
import TickerService from '@bg-services/ticker.service';
import type { IExperienceOptions } from './interfaces';
import { ArrangementVariant } from './LuckyLennyExperience/managers/symbols/SymbolManager';
import LennyExperienceBase from './LuckyLennyExperience/setup/LennyExperienceBase';
import { LuckyLennyAssetLoader } from './LuckyLennyExperience/utils/LuckyLennyAssetLoader';
import { LuckyLennyEvents } from './enums';

// LennyExperience is the main class for the LuckyLenny game.
// it is responsible for basic setup and event handling
class LennyExperience extends LennyExperienceBase {
    constructor({
        canvas,
        eventManager,
        enableDebug = false,
        onLoadComplete,
        width,
        height,
    }: IExperienceOptions) {
        super({
            canvas,
            eventManager,
            enableDebug,
            onLoadComplete,
            width,
            height,
        });
        this.setup();
        this.setupEvents();
    }

    protected setup = async (): Promise<void> => {
        this.viewport = new PixiViewport(
            {
                width: this.width,
                height: this.height,
                canvas: this.canvas,
                eventManager: this.eventManager,
            },
            { fullScreen: true, interactive: true },
        );

        await this.viewport.initialize();
        await LuckyLennyAssetLoader.ensureLoaded();
        await this.initialize();

        TickerService.addListener(this.update);
        TickerService.start();

        this.isReady = true;
        this.onLoadComplete();

        this.eventUnsubscribers.push(
            this.eventManager.on(LuckyLennyEvents.endGame, () => {
                this.eventManager.trigger(LuckyLennyEvents.newGame);
            }),
            this.eventManager.on(LuckyLennyEvents.totalWinAnimationEnd, () => {
                this.eventManager.trigger(LuckyLennyEvents.endGame);
            }),
        );
    };

    // Tmp version for testing
    private setupEvents = (): void => {
        window.addEventListener('keydown', this.handleKeyDown);

        // Add cleanup function to eventUnsubscribers to ensure proper cleanup
        this.eventUnsubscribers.push(() => {
            window.removeEventListener('keydown', this.handleKeyDown);
        });
    };

    private handleKeyDown = (event: KeyboardEvent): void => {
        // Layout variant controls
        if (event.code === 'Digit1') {
            event.preventDefault();
            this.lennySceneManager.setArrangementVariant(ArrangementVariant.FIXED_ORDER);
            Logger.log('LennyExperience: Layout variant set to FIXED_ORDER', 'lenny');

            return;
        }

        if (event.code === 'Digit2') {
            event.preventDefault();
            this.lennySceneManager.setArrangementVariant(ArrangementVariant.PREDEFINED);
            Logger.log('LennyExperience: Layout variant set to PREDEFINED', 'lenny');
        }
    };
}

export default LennyExperience;
